2025-08-01 15:16:06,880 - INFO - 开始自动环境修复
2025-08-01 15:16:06,881 - INFO - 创建测试文件: test_simple.py
2025-08-01 15:16:06,881 - INFO - 第 1 次尝试修复
2025-08-01 15:16:06,881 - INFO - 执行命令: python hello.py
2025-08-01 15:16:07,054 - WARNING - 命令执行失败: python hello.py (退出码: 1)
2025-08-01 15:16:07,055 - WARNING - 环境测试失败，退出码: 1
2025-08-01 15:16:07,055 - WARNING - 错误输出: Traceback (most recent call last):
  File "G:\work\auto\hello.py", line 20, in <module>
    sys.exit(main())
             ~~~~^^
  File "G:\work\auto\hello.py", line 11, in main
    print("\U0001f389 Hello World!")
    ~~~~~^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f389' in position 0: illegal multibyte sequence

2025-08-01 15:16:07,063 - INFO - 错误分析: 未知错误
2025-08-01 15:16:07,063 - INFO - 修复命令: ['echo 检查系统环境']
2025-08-01 15:16:07,064 - INFO - 置信度: 0.3
2025-08-01 15:16:07,064 - INFO - 执行修复命令 1/1: echo 检查系统环境
2025-08-01 15:16:07,064 - INFO - 执行命令: echo 检查系统环境
2025-08-01 15:16:07,076 - INFO - 命令执行成功: echo 检查系统环境
2025-08-01 15:16:07,076 - INFO - 修复命令成功: echo 检查系统环境
2025-08-01 15:16:10,077 - INFO - 第 2 次尝试修复
2025-08-01 15:16:10,077 - INFO - 执行命令: python hello.py
2025-08-01 15:16:10,291 - WARNING - 命令执行失败: python hello.py (退出码: 1)
2025-08-01 15:16:10,291 - WARNING - 环境测试失败，退出码: 1
2025-08-01 15:16:10,291 - WARNING - 错误输出: Traceback (most recent call last):
  File "G:\work\auto\hello.py", line 20, in <module>
    sys.exit(main())
             ~~~~^^
  File "G:\work\auto\hello.py", line 11, in main
    print("\U0001f389 Hello World!")
    ~~~~~^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f389' in position 0: illegal multibyte sequence

2025-08-01 15:16:10,291 - INFO - 错误分析: 未知错误
2025-08-01 15:16:10,291 - INFO - 修复命令: ['echo 检查系统环境']
2025-08-01 15:16:10,291 - INFO - 置信度: 0.3
2025-08-01 15:16:10,292 - INFO - 执行修复命令 1/1: echo 检查系统环境
2025-08-01 15:16:10,292 - INFO - 执行命令: echo 检查系统环境
2025-08-01 15:16:10,302 - INFO - 命令执行成功: echo 检查系统环境
2025-08-01 15:16:10,303 - INFO - 修复命令成功: echo 检查系统环境
2025-08-01 15:16:13,304 - INFO - 第 3 次尝试修复
2025-08-01 15:16:13,304 - INFO - 执行命令: python hello.py
2025-08-01 15:16:13,504 - WARNING - 命令执行失败: python hello.py (退出码: 1)
2025-08-01 15:16:13,504 - WARNING - 环境测试失败，退出码: 1
2025-08-01 15:16:13,504 - WARNING - 错误输出: Traceback (most recent call last):
  File "G:\work\auto\hello.py", line 20, in <module>
    sys.exit(main())
             ~~~~^^
  File "G:\work\auto\hello.py", line 11, in main
    print("\U0001f389 Hello World!")
    ~~~~~^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f389' in position 0: illegal multibyte sequence

2025-08-01 15:16:13,504 - INFO - 错误分析: 未知错误
2025-08-01 15:16:13,505 - INFO - 修复命令: ['echo 检查系统环境']
2025-08-01 15:16:13,505 - INFO - 置信度: 0.3
2025-08-01 15:16:13,505 - INFO - 执行修复命令 1/1: echo 检查系统环境
2025-08-01 15:16:13,505 - INFO - 执行命令: echo 检查系统环境
2025-08-01 15:16:13,515 - INFO - 命令执行成功: echo 检查系统环境
2025-08-01 15:16:13,515 - INFO - 修复命令成功: echo 检查系统环境
2025-08-01 15:16:16,516 - INFO - 第 4 次尝试修复
2025-08-01 15:16:16,516 - INFO - 执行命令: python hello.py
2025-08-01 15:16:16,696 - WARNING - 命令执行失败: python hello.py (退出码: 1)
2025-08-01 15:16:16,696 - WARNING - 环境测试失败，退出码: 1
2025-08-01 15:16:16,696 - WARNING - 错误输出: Traceback (most recent call last):
  File "G:\work\auto\hello.py", line 20, in <module>
    sys.exit(main())
             ~~~~^^
  File "G:\work\auto\hello.py", line 11, in main
    print("\U0001f389 Hello World!")
    ~~~~~^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f389' in position 0: illegal multibyte sequence

2025-08-01 15:16:16,696 - INFO - 错误分析: 未知错误
2025-08-01 15:16:16,696 - INFO - 修复命令: ['echo 检查系统环境']
2025-08-01 15:16:16,696 - INFO - 置信度: 0.3
2025-08-01 15:16:16,697 - INFO - 执行修复命令 1/1: echo 检查系统环境
2025-08-01 15:16:16,697 - INFO - 执行命令: echo 检查系统环境
2025-08-01 15:16:16,712 - INFO - 命令执行成功: echo 检查系统环境
2025-08-01 15:16:16,712 - INFO - 修复命令成功: echo 检查系统环境
2025-08-01 15:16:19,713 - INFO - 第 5 次尝试修复
2025-08-01 15:16:19,714 - INFO - 执行命令: python hello.py
2025-08-01 15:16:19,880 - WARNING - 命令执行失败: python hello.py (退出码: 1)
2025-08-01 15:16:19,880 - WARNING - 环境测试失败，退出码: 1
2025-08-01 15:16:19,880 - WARNING - 错误输出: Traceback (most recent call last):
  File "G:\work\auto\hello.py", line 20, in <module>
    sys.exit(main())
             ~~~~^^
  File "G:\work\auto\hello.py", line 11, in main
    print("\U0001f389 Hello World!")
    ~~~~~^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f389' in position 0: illegal multibyte sequence

2025-08-01 15:16:19,880 - INFO - 错误分析: 未知错误
2025-08-01 15:16:19,880 - INFO - 修复命令: ['echo 检查系统环境']
2025-08-01 15:16:19,880 - INFO - 置信度: 0.3
2025-08-01 15:16:19,880 - INFO - 执行修复命令 1/1: echo 检查系统环境
2025-08-01 15:16:19,880 - INFO - 执行命令: echo 检查系统环境
2025-08-01 15:16:19,891 - INFO - 命令执行成功: echo 检查系统环境
2025-08-01 15:16:19,891 - INFO - 修复命令成功: echo 检查系统环境
2025-08-01 15:16:22,892 - ERROR - 经过 5 次尝试后仍无法修复环境
2025-08-01 15:16:47,113 - INFO - 开始自动环境修复
2025-08-01 15:16:47,114 - INFO - 创建测试文件: test_simple.py
2025-08-01 15:16:47,114 - INFO - 第 1 次尝试修复
2025-08-01 15:16:47,114 - INFO - 执行命令: python hello.py
2025-08-01 15:16:47,325 - INFO - 命令执行成功: python hello.py
2025-08-01 15:16:47,325 - INFO - 环境测试通过！修复成功
2025-08-01 15:17:04,362 - INFO - 开始自动环境修复
2025-08-01 15:17:04,362 - INFO - 创建测试文件: test_simple.py
2025-08-01 15:17:04,363 - INFO - 第 1 次尝试修复
2025-08-01 15:17:04,363 - INFO - 执行命令: node test_node.js
2025-08-01 15:17:04,532 - INFO - 命令执行成功: node test_node.js
2025-08-01 15:17:04,532 - INFO - 环境测试通过！修复成功
2025-08-01 15:17:33,405 - INFO - 开始自动环境修复
2025-08-01 15:17:33,406 - INFO - 创建测试文件: test_simple.py
2025-08-01 15:17:33,406 - INFO - 第 1 次尝试修复
2025-08-01 15:17:33,406 - INFO - 执行命令: python
2025-08-01 15:17:33,406 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:17:33,406 - WARNING - 环境测试失败，退出码: -1
2025-08-01 15:17:33,406 - WARNING - 错误输出: [WinError 267] 目录名称无效。
2025-08-01 15:17:33,406 - INFO - 错误分析: 未知错误
2025-08-01 15:17:33,406 - INFO - 修复命令: ['echo 检查系统环境']
2025-08-01 15:17:33,406 - INFO - 置信度: 0.3
2025-08-01 15:17:33,406 - INFO - 执行修复命令 1/1: echo 检查系统环境
2025-08-01 15:17:33,406 - INFO - 执行命令: echo 检查系统环境
2025-08-01 15:17:33,406 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:17:33,406 - WARNING - 修复命令失败: echo 检查系统环境
2025-08-01 15:17:33,406 - WARNING - 错误: [WinError 267] 目录名称无效。
2025-08-01 15:17:36,407 - INFO - 第 2 次尝试修复
2025-08-01 15:17:36,407 - INFO - 执行命令: python
2025-08-01 15:17:36,407 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:17:36,407 - WARNING - 环境测试失败，退出码: -1
2025-08-01 15:17:36,407 - WARNING - 错误输出: [WinError 267] 目录名称无效。
2025-08-01 15:17:36,407 - INFO - 错误分析: 未知错误
2025-08-01 15:17:36,407 - INFO - 修复命令: ['echo 检查系统环境']
2025-08-01 15:17:36,408 - INFO - 置信度: 0.3
2025-08-01 15:17:36,408 - INFO - 执行修复命令 1/1: echo 检查系统环境
2025-08-01 15:17:36,408 - INFO - 执行命令: echo 检查系统环境
2025-08-01 15:17:36,408 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:17:36,408 - WARNING - 修复命令失败: echo 检查系统环境
2025-08-01 15:17:36,408 - WARNING - 错误: [WinError 267] 目录名称无效。
2025-08-01 15:17:39,408 - INFO - 第 3 次尝试修复
2025-08-01 15:17:39,408 - INFO - 执行命令: python
2025-08-01 15:17:39,409 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:17:39,409 - WARNING - 环境测试失败，退出码: -1
2025-08-01 15:17:39,409 - WARNING - 错误输出: [WinError 267] 目录名称无效。
2025-08-01 15:17:39,409 - INFO - 错误分析: 未知错误
2025-08-01 15:17:39,409 - INFO - 修复命令: ['echo 检查系统环境']
2025-08-01 15:17:39,409 - INFO - 置信度: 0.3
2025-08-01 15:17:39,409 - INFO - 执行修复命令 1/1: echo 检查系统环境
2025-08-01 15:17:39,409 - INFO - 执行命令: echo 检查系统环境
2025-08-01 15:17:39,409 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:17:39,409 - WARNING - 修复命令失败: echo 检查系统环境
2025-08-01 15:17:39,409 - WARNING - 错误: [WinError 267] 目录名称无效。
2025-08-01 15:17:42,410 - INFO - 第 4 次尝试修复
2025-08-01 15:17:42,410 - INFO - 执行命令: python
2025-08-01 15:17:42,410 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:17:42,410 - WARNING - 环境测试失败，退出码: -1
2025-08-01 15:17:42,410 - WARNING - 错误输出: [WinError 267] 目录名称无效。
2025-08-01 15:17:42,410 - INFO - 错误分析: 未知错误
2025-08-01 15:17:42,410 - INFO - 修复命令: ['echo 检查系统环境']
2025-08-01 15:17:42,411 - INFO - 置信度: 0.3
2025-08-01 15:17:42,411 - INFO - 执行修复命令 1/1: echo 检查系统环境
2025-08-01 15:17:42,411 - INFO - 执行命令: echo 检查系统环境
2025-08-01 15:17:42,411 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:17:42,411 - WARNING - 修复命令失败: echo 检查系统环境
2025-08-01 15:17:42,411 - WARNING - 错误: [WinError 267] 目录名称无效。
2025-08-01 15:17:45,412 - INFO - 第 5 次尝试修复
2025-08-01 15:17:45,412 - INFO - 执行命令: python
2025-08-01 15:17:45,412 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:17:45,413 - WARNING - 环境测试失败，退出码: -1
2025-08-01 15:17:45,413 - WARNING - 错误输出: [WinError 267] 目录名称无效。
2025-08-01 15:17:45,413 - INFO - 错误分析: 未知错误
2025-08-01 15:17:45,413 - INFO - 修复命令: ['echo 检查系统环境']
2025-08-01 15:17:45,413 - INFO - 置信度: 0.3
2025-08-01 15:17:45,413 - INFO - 执行修复命令 1/1: echo 检查系统环境
2025-08-01 15:17:45,413 - INFO - 执行命令: echo 检查系统环境
2025-08-01 15:17:45,413 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:17:45,413 - WARNING - 修复命令失败: echo 检查系统环境
2025-08-01 15:17:45,413 - WARNING - 错误: [WinError 267] 目录名称无效。
2025-08-01 15:17:48,413 - ERROR - 经过 5 次尝试后仍无法修复环境
2025-08-01 15:17:49,584 - INFO - 开始自动环境修复
2025-08-01 15:17:49,585 - INFO - 创建测试文件: test_simple.py
2025-08-01 15:17:49,585 - INFO - 第 1 次尝试修复
2025-08-01 15:17:49,585 - INFO - 执行命令: node
2025-08-01 15:17:49,585 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:17:49,585 - WARNING - 环境测试失败，退出码: -1
2025-08-01 15:17:49,585 - WARNING - 错误输出: [WinError 267] 目录名称无效。
2025-08-01 15:17:49,585 - INFO - 错误分析: 未知错误
2025-08-01 15:17:49,585 - INFO - 修复命令: ['echo 检查系统环境']
2025-08-01 15:17:49,585 - INFO - 置信度: 0.3
2025-08-01 15:17:49,585 - INFO - 执行修复命令 1/1: echo 检查系统环境
2025-08-01 15:17:49,585 - INFO - 执行命令: echo 检查系统环境
2025-08-01 15:17:49,586 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:17:49,586 - WARNING - 修复命令失败: echo 检查系统环境
2025-08-01 15:17:49,586 - WARNING - 错误: [WinError 267] 目录名称无效。
2025-08-01 15:17:52,587 - INFO - 第 2 次尝试修复
2025-08-01 15:17:52,587 - INFO - 执行命令: node
2025-08-01 15:17:52,587 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:17:52,587 - WARNING - 环境测试失败，退出码: -1
2025-08-01 15:17:52,587 - WARNING - 错误输出: [WinError 267] 目录名称无效。
2025-08-01 15:17:52,587 - INFO - 错误分析: 未知错误
2025-08-01 15:17:52,587 - INFO - 修复命令: ['echo 检查系统环境']
2025-08-01 15:17:52,587 - INFO - 置信度: 0.3
2025-08-01 15:17:52,587 - INFO - 执行修复命令 1/1: echo 检查系统环境
2025-08-01 15:17:52,587 - INFO - 执行命令: echo 检查系统环境
2025-08-01 15:17:52,587 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:17:52,587 - WARNING - 修复命令失败: echo 检查系统环境
2025-08-01 15:17:52,587 - WARNING - 错误: [WinError 267] 目录名称无效。
2025-08-01 15:17:55,588 - INFO - 第 3 次尝试修复
2025-08-01 15:17:55,588 - INFO - 执行命令: node
2025-08-01 15:17:55,588 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:17:55,588 - WARNING - 环境测试失败，退出码: -1
2025-08-01 15:17:55,588 - WARNING - 错误输出: [WinError 267] 目录名称无效。
2025-08-01 15:17:55,588 - INFO - 错误分析: 未知错误
2025-08-01 15:17:55,588 - INFO - 修复命令: ['echo 检查系统环境']
2025-08-01 15:17:55,588 - INFO - 置信度: 0.3
2025-08-01 15:17:55,588 - INFO - 执行修复命令 1/1: echo 检查系统环境
2025-08-01 15:17:55,588 - INFO - 执行命令: echo 检查系统环境
2025-08-01 15:17:55,589 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:17:55,589 - WARNING - 修复命令失败: echo 检查系统环境
2025-08-01 15:17:55,589 - WARNING - 错误: [WinError 267] 目录名称无效。
2025-08-01 15:17:58,590 - INFO - 第 4 次尝试修复
2025-08-01 15:17:58,590 - INFO - 执行命令: node
2025-08-01 15:17:58,590 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:17:58,590 - WARNING - 环境测试失败，退出码: -1
2025-08-01 15:17:58,590 - WARNING - 错误输出: [WinError 267] 目录名称无效。
2025-08-01 15:17:58,590 - INFO - 错误分析: 未知错误
2025-08-01 15:17:58,590 - INFO - 修复命令: ['echo 检查系统环境']
2025-08-01 15:17:58,590 - INFO - 置信度: 0.3
2025-08-01 15:17:58,590 - INFO - 执行修复命令 1/1: echo 检查系统环境
2025-08-01 15:17:58,590 - INFO - 执行命令: echo 检查系统环境
2025-08-01 15:17:58,590 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:17:58,590 - WARNING - 修复命令失败: echo 检查系统环境
2025-08-01 15:17:58,590 - WARNING - 错误: [WinError 267] 目录名称无效。
2025-08-01 15:18:01,591 - INFO - 第 5 次尝试修复
2025-08-01 15:18:01,591 - INFO - 执行命令: node
2025-08-01 15:18:01,591 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:18:01,591 - WARNING - 环境测试失败，退出码: -1
2025-08-01 15:18:01,591 - WARNING - 错误输出: [WinError 267] 目录名称无效。
2025-08-01 15:18:01,591 - INFO - 错误分析: 未知错误
2025-08-01 15:18:01,591 - INFO - 修复命令: ['echo 检查系统环境']
2025-08-01 15:18:01,591 - INFO - 置信度: 0.3
2025-08-01 15:18:01,592 - INFO - 执行修复命令 1/1: echo 检查系统环境
2025-08-01 15:18:01,592 - INFO - 执行命令: echo 检查系统环境
2025-08-01 15:18:01,592 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:18:01,592 - WARNING - 修复命令失败: echo 检查系统环境
2025-08-01 15:18:01,592 - WARNING - 错误: [WinError 267] 目录名称无效。
2025-08-01 15:18:04,593 - ERROR - 经过 5 次尝试后仍无法修复环境
2025-08-01 15:18:05,788 - INFO - 开始自动环境修复
2025-08-01 15:18:05,789 - INFO - 创建测试文件: test_simple.py
2025-08-01 15:18:05,789 - INFO - 第 1 次尝试修复
2025-08-01 15:18:05,789 - INFO - 执行命令: python
2025-08-01 15:18:05,789 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:18:05,789 - WARNING - 环境测试失败，退出码: -1
2025-08-01 15:18:05,789 - WARNING - 错误输出: [WinError 267] 目录名称无效。
2025-08-01 15:18:05,789 - INFO - 错误分析: 未知错误
2025-08-01 15:18:05,789 - INFO - 修复命令: ['echo 检查系统环境']
2025-08-01 15:18:05,789 - INFO - 置信度: 0.3
2025-08-01 15:18:05,789 - INFO - 执行修复命令 1/1: echo 检查系统环境
2025-08-01 15:18:05,789 - INFO - 执行命令: echo 检查系统环境
2025-08-01 15:18:05,789 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:18:05,789 - WARNING - 修复命令失败: echo 检查系统环境
2025-08-01 15:18:05,789 - WARNING - 错误: [WinError 267] 目录名称无效。
2025-08-01 15:18:08,790 - INFO - 第 2 次尝试修复
2025-08-01 15:18:08,790 - INFO - 执行命令: python
2025-08-01 15:18:08,791 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:18:08,791 - WARNING - 环境测试失败，退出码: -1
2025-08-01 15:18:08,791 - WARNING - 错误输出: [WinError 267] 目录名称无效。
2025-08-01 15:18:08,791 - INFO - 错误分析: 未知错误
2025-08-01 15:18:08,791 - INFO - 修复命令: ['echo 检查系统环境']
2025-08-01 15:18:08,791 - INFO - 置信度: 0.3
2025-08-01 15:18:08,791 - INFO - 执行修复命令 1/1: echo 检查系统环境
2025-08-01 15:18:08,791 - INFO - 执行命令: echo 检查系统环境
2025-08-01 15:18:08,791 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:18:08,791 - WARNING - 修复命令失败: echo 检查系统环境
2025-08-01 15:18:08,791 - WARNING - 错误: [WinError 267] 目录名称无效。
2025-08-01 15:18:11,792 - INFO - 第 3 次尝试修复
2025-08-01 15:18:11,792 - INFO - 执行命令: python
2025-08-01 15:18:11,792 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:18:11,792 - WARNING - 环境测试失败，退出码: -1
2025-08-01 15:18:11,792 - WARNING - 错误输出: [WinError 267] 目录名称无效。
2025-08-01 15:18:11,792 - INFO - 错误分析: 未知错误
2025-08-01 15:18:11,792 - INFO - 修复命令: ['echo 检查系统环境']
2025-08-01 15:18:11,792 - INFO - 置信度: 0.3
2025-08-01 15:18:11,792 - INFO - 执行修复命令 1/1: echo 检查系统环境
2025-08-01 15:18:11,792 - INFO - 执行命令: echo 检查系统环境
2025-08-01 15:18:11,792 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:18:11,792 - WARNING - 修复命令失败: echo 检查系统环境
2025-08-01 15:18:11,792 - WARNING - 错误: [WinError 267] 目录名称无效。
2025-08-01 15:18:14,793 - INFO - 第 4 次尝试修复
2025-08-01 15:18:14,793 - INFO - 执行命令: python
2025-08-01 15:18:14,794 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:18:14,794 - WARNING - 环境测试失败，退出码: -1
2025-08-01 15:18:14,794 - WARNING - 错误输出: [WinError 267] 目录名称无效。
2025-08-01 15:18:14,794 - INFO - 错误分析: 未知错误
2025-08-01 15:18:14,794 - INFO - 修复命令: ['echo 检查系统环境']
2025-08-01 15:18:14,794 - INFO - 置信度: 0.3
2025-08-01 15:18:14,794 - INFO - 执行修复命令 1/1: echo 检查系统环境
2025-08-01 15:18:14,794 - INFO - 执行命令: echo 检查系统环境
2025-08-01 15:18:14,794 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:18:14,794 - WARNING - 修复命令失败: echo 检查系统环境
2025-08-01 15:18:14,794 - WARNING - 错误: [WinError 267] 目录名称无效。
2025-08-01 15:18:17,795 - INFO - 第 5 次尝试修复
2025-08-01 15:18:17,795 - INFO - 执行命令: python
2025-08-01 15:18:17,795 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:18:17,795 - WARNING - 环境测试失败，退出码: -1
2025-08-01 15:18:17,795 - WARNING - 错误输出: [WinError 267] 目录名称无效。
2025-08-01 15:18:17,795 - INFO - 错误分析: 未知错误
2025-08-01 15:18:17,795 - INFO - 修复命令: ['echo 检查系统环境']
2025-08-01 15:18:17,795 - INFO - 置信度: 0.3
2025-08-01 15:18:17,795 - INFO - 执行修复命令 1/1: echo 检查系统环境
2025-08-01 15:18:17,795 - INFO - 执行命令: echo 检查系统环境
2025-08-01 15:18:17,795 - ERROR - 命令执行异常: [WinError 267] 目录名称无效。
2025-08-01 15:18:17,795 - WARNING - 修复命令失败: echo 检查系统环境
2025-08-01 15:18:17,795 - WARNING - 错误: [WinError 267] 目录名称无效。
2025-08-01 15:18:20,796 - ERROR - 经过 5 次尝试后仍无法修复环境
