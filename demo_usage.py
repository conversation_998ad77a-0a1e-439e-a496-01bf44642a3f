#!/usr/bin/env python3
"""
演示如何使用智能环境管理器
"""

import subprocess
import sys
import os

def run_demo():
    """运行演示"""
    print("🚀 智能环境配置管理器演示")
    print("=" * 50)
    
    # 演示场景1：Python环境测试
    print("\n📋 场景1: 测试Python环境")
    print("命令: python3 enhanced_env_manager.py 'python3 hello.py'")
    
    try:
        result = subprocess.run([
            sys.executable, 
            "enhanced_env_manager.py", 
            "python3 hello.py"
        ], capture_output=True, text=True, timeout=60)
        
        print(f"退出码: {result.returncode}")
        if result.stdout:
            print("输出:")
            print(result.stdout)
        if result.stderr:
            print("错误:")
            print(result.stderr)
            
    except subprocess.TimeoutExpired:
        print("⏰ 演示超时")
    except Exception as e:
        print(f"💥 演示失败: {e}")
    
    print("\n" + "=" * 50)
    print("📚 使用说明:")
    print("1. 基本用法: python3 enhanced_env_manager.py '<测试命令>'")
    print("2. 指定目录: python3 enhanced_env_manager.py '<测试命令>' <工作目录>")
    print("3. 禁用LLM: python3 enhanced_env_manager.py '<测试命令>' --no-llm")
    print("\n📝 示例:")
    print("  python3 enhanced_env_manager.py 'python3 hello.py'")
    print("  python3 enhanced_env_manager.py 'node app.js' /path/to/project")
    print("  python3 enhanced_env_manager.py 'npm start' . --no-llm")
    
    print("\n🔧 功能特性:")
    print("✅ 自动检测环境问题")
    print("✅ LLM智能错误分析")
    print("✅ 自动生成修复脚本")
    print("✅ 循环修复直到成功")
    print("✅ 详细的修复历史记录")
    print("✅ 本地规则备用分析")

if __name__ == "__main__":
    run_demo()
