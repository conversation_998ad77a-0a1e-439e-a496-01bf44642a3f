#!/usr/bin/env python3
"""
智能环境配置管理器最终演示
"""

import subprocess
import sys
import os
import time

def run_demo_test(command, description):
    """运行演示测试"""
    print(f"\n{'='*60}")
    print(f"测试: {description}")
    print(f"命令: {command}")
    print('='*60)
    
    try:
        start_time = time.time()
        result = subprocess.run([
            sys.executable, 
            "simple_env_manager.py"
        ] + command.split(), 
        capture_output=True, 
        text=True, 
        timeout=120
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"执行时间: {duration:.2f}秒")
        print(f"退出码: {result.returncode}")
        
        if result.stdout:
            print("\n标准输出:")
            print(result.stdout)
        
        if result.stderr:
            print("\n错误输出:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("\n✅ 测试通过")
        else:
            print("\n❌ 测试失败")
            
    except subprocess.TimeoutExpired:
        print("⏰ 测试超时")
    except Exception as e:
        print(f"💥 测试异常: {e}")

def main():
    """主演示函数"""
    print("🚀 智能环境配置管理器演示")
    print("这个系统能够自动检测、分析和修复项目环境问题")
    
    # 检查必要文件是否存在
    required_files = ["simple_env_manager.py", "hello.py", "test_node.js"]
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        return
    
    # 演示测试用例
    test_cases = [
        ("python hello.py", "Python环境测试"),
        ("node test_node.js", "Node.js环境测试"),
        ("python test_simple.py", "自动生成的测试文件"),
    ]
    
    for command, description in test_cases:
        run_demo_test(command, description)
        time.sleep(1)  # 短暂延迟
    
    print(f"\n{'='*60}")
    print("演示完成！")
    print("\n📚 系统特性总结:")
    print("✅ 自动检测环境问题")
    print("✅ 智能错误分析")
    print("✅ 自动生成修复方案")
    print("✅ 循环修复直到成功")
    print("✅ 跨平台支持 (Windows/Linux/macOS)")
    print("✅ 详细的执行日志")
    
    print("\n🔧 支持的环境:")
    print("• Python 3.x")
    print("• Node.js")
    print("• 基础系统工具")
    
    print("\n📖 使用方法:")
    print("python simple_env_manager.py '<测试命令>' [工作目录]")
    
    print("\n📁 生成的文件:")
    generated_files = [
        "test_simple.py",
        "simple_env_manager.log"
    ]
    
    for file in generated_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} (未生成)")

if __name__ == "__main__":
    main()
