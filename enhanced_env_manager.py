#!/usr/bin/env python3
"""
增强版智能环境配置管理器
集成LLM分析的自动环境修复系统
"""

import os
import sys
import json
import subprocess
import time
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path

# 导入LLM分析器
try:
    from llm_analyzer import LLMAnalyzer, LLMResponse
    LLM_AVAILABLE = True
except ImportError:
    LLM_AVAILABLE = False
    logger.warning("LLM分析器不可用，将使用本地规则分析")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_env_manager.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ExecutionResult:
    """执行结果数据类"""
    command: str
    exit_code: int
    stdout: str
    stderr: str
    success: bool
    working_dir: str
    timestamp: float

@dataclass
class FixAttempt:
    """修复尝试记录"""
    attempt_number: int
    error_analysis: str
    fix_commands: List[str]
    execution_results: List[ExecutionResult]
    success: bool
    timestamp: float

class EnhancedEnvironmentManager:
    """增强版环境管理器"""
    
    def __init__(self, max_attempts: int = 5, use_llm: bool = True):
        self.max_attempts = max_attempts
        self.use_llm = use_llm and LLM_AVAILABLE
        self.attempt_count = 0
        self.fix_history: List[FixAttempt] = []
        
        # 初始化LLM分析器
        if self.use_llm:
            self.llm_analyzer = LLMAnalyzer()
            logger.info("LLM分析器已启用")
        else:
            self.llm_analyzer = None
            logger.info("使用本地规则分析")
    
    def execute_command(self, command: str, working_dir: str = ".", timeout: int = 300) -> ExecutionResult:
        """执行命令并返回详细结果"""
        logger.info(f"执行命令: {command} (工作目录: {working_dir})")
        
        start_time = time.time()
        
        try:
            process = subprocess.run(
                command,
                shell=True,
                cwd=working_dir,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            
            result = ExecutionResult(
                command=command,
                exit_code=process.returncode,
                stdout=process.stdout,
                stderr=process.stderr,
                success=process.returncode == 0,
                working_dir=working_dir,
                timestamp=start_time
            )
            
            if result.success:
                logger.info(f"✅ 命令执行成功: {command}")
            else:
                logger.warning(f"❌ 命令执行失败: {command} (退出码: {result.exit_code})")
            
            return result
            
        except subprocess.TimeoutExpired:
            logger.error(f"⏰ 命令执行超时: {command}")
            return ExecutionResult(
                command=command,
                exit_code=-1,
                stdout="",
                stderr="命令执行超时",
                success=False,
                working_dir=working_dir,
                timestamp=start_time
            )
        except Exception as e:
            logger.error(f"💥 命令执行异常: {e}")
            return ExecutionResult(
                command=command,
                exit_code=-1,
                stdout="",
                stderr=str(e),
                success=False,
                working_dir=working_dir,
                timestamp=start_time
            )
    
    def analyze_error_with_llm(self, result: ExecutionResult) -> LLMResponse:
        """使用LLM分析错误"""
        error_info = {
            'command': result.command,
            'exit_code': result.exit_code,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'working_dir': result.working_dir
        }
        
        if self.llm_analyzer:
            return self.llm_analyzer.analyze_error_with_llm(error_info)
        else:
            # 使用本地规则分析
            return self._local_error_analysis(result)
    
    def _local_error_analysis(self, result: ExecutionResult) -> LLMResponse:
        """本地错误分析（备用方案）"""
        stderr = (result.stderr or "").lower()
        command = result.command.lower()

        # 检测操作系统
        is_windows = os.name == 'nt'

        if 'python3: not found' in stderr or 'python: not found' in stderr or "'python3' is not recognized" in stderr:
            if is_windows:
                return LLMResponse(
                    analysis="Python未安装或不在PATH中",
                    fix_commands=[
                        "python --version",
                        "py --version",
                        "where python"
                    ],
                    explanation="Windows系统中Python可能已安装但命令不同，尝试使用python或py命令",
                    confidence=0.8
                )
            else:
                return LLMResponse(
                    analysis="Python3未安装或不在PATH中",
                    fix_commands=[
                        "sudo apt-get update",
                        "sudo apt-get install python3 python3-pip",
                        "python3 --version"
                    ],
                    explanation="需要安装Python3运行环境",
                    confidence=0.9
                )
        elif 'node: not found' in stderr or "'node' is not recognized" in stderr:
            if is_windows:
                return LLMResponse(
                    analysis="Node.js未安装",
                    fix_commands=[
                        "winget install OpenJS.NodeJS",
                        "node --version"
                    ],
                    explanation="需要安装Node.js运行环境",
                    confidence=0.9
                )
            else:
                return LLMResponse(
                    analysis="Node.js未安装",
                    fix_commands=[
                        "curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -",
                        "sudo apt-get install -y nodejs",
                        "node --version"
                    ],
                    explanation="需要安装Node.js运行环境",
                    confidence=0.9
                )
        else:
            if is_windows:
                return LLMResponse(
                    analysis="未知错误",
                    fix_commands=["echo 检查系统环境"],
                    explanation="建议检查系统环境配置",
                    confidence=0.3
                )
            else:
                return LLMResponse(
                    analysis="未知错误",
                    fix_commands=["sudo apt-get update"],
                    explanation="建议更新系统包管理器",
                    confidence=0.3
                )
    
    def create_comprehensive_test_file(self, filename: str = "comprehensive_test.py") -> str:
        """创建综合测试文件"""
        test_content = '''#!/usr/bin/env python3
"""
综合环境测试文件
测试Python环境、基础模块和系统工具
"""

import sys
import os
import subprocess
import platform

def test_python_environment():
    """测试Python环境"""
    print("=== Python环境测试 ===")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"平台信息: {platform.platform()}")
    return True

def test_basic_modules():
    """测试基础模块"""
    print("\\n=== 基础模块测试 ===")
    modules = ['json', 'subprocess', 'pathlib', 'urllib', 'sqlite3']
    
    for module in modules:
        try:
            __import__(module)
            print(f"✅ {module}: 可用")
        except ImportError:
            print(f"❌ {module}: 不可用")
            return False
    
    return True

def test_system_tools():
    """测试系统工具"""
    print("\\n=== 系统工具测试 ===")
    tools = ['git', 'curl', 'wget']
    
    for tool in tools:
        try:
            result = subprocess.run([tool, '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print(f"✅ {tool}: 可用")
            else:
                print(f"⚠️ {tool}: 可能不可用")
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print(f"❌ {tool}: 不可用")

def test_network_connectivity():
    """测试网络连接"""
    print("\\n=== 网络连接测试 ===")
    try:
        import urllib.request
        urllib.request.urlopen('https://www.google.com', timeout=5)
        print("✅ 网络连接: 正常")
        return True
    except:
        print("❌ 网络连接: 异常")
        return False

def main():
    """主测试函数"""
    print("🚀 开始综合环境测试...")
    
    success = True
    success &= test_python_environment()
    success &= test_basic_modules()
    test_system_tools()  # 系统工具不影响主要测试结果
    test_network_connectivity()  # 网络连接不影响主要测试结果
    
    print("\\n" + "="*50)
    if success:
        print("🎉 环境测试通过！系统已准备就绪")
        return 0
    else:
        print("💥 环境测试失败！需要修复环境")
        return 1

if __name__ == "__main__":
    sys.exit(main())
'''
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        os.chmod(filename, 0o755)
        logger.info(f"创建综合测试文件: {filename}")
        return filename
    
    def execute_fix_commands(self, commands: List[str], working_dir: str = ".") -> List[ExecutionResult]:
        """执行修复命令列表"""
        results = []
        
        for i, command in enumerate(commands, 1):
            logger.info(f"执行修复命令 {i}/{len(commands)}: {command}")
            
            result = self.execute_command(command, working_dir)
            results.append(result)
            
            if not result.success:
                logger.warning(f"修复命令失败: {command}")
                logger.warning(f"错误: {result.stderr}")
            else:
                logger.info(f"修复命令成功: {command}")
            
            # 命令间短暂延迟
            time.sleep(1)
        
        return results
    
    def auto_fix_environment(self, test_command: str, working_dir: str = ".") -> bool:
        """自动修复环境主循环"""
        logger.info("🚀 开始智能环境修复流程")
        
        # 创建测试文件
        test_file = self.create_comprehensive_test_file()
        
        while self.attempt_count < self.max_attempts:
            self.attempt_count += 1
            logger.info(f"\\n📋 第 {self.attempt_count}/{self.max_attempts} 次修复尝试")
            
            # 执行测试命令
            test_result = self.execute_command(test_command, working_dir)
            
            if test_result.success:
                logger.info("🎉 环境测试通过！修复成功完成")
                
                # 记录成功的修复尝试
                success_attempt = FixAttempt(
                    attempt_number=self.attempt_count,
                    error_analysis="环境测试通过",
                    fix_commands=[],
                    execution_results=[test_result],
                    success=True,
                    timestamp=time.time()
                )
                self.fix_history.append(success_attempt)
                
                return True
            
            logger.warning(f"❌ 环境测试失败 (退出码: {test_result.exit_code})")
            logger.warning(f"错误输出: {test_result.stderr}")
            
            # 使用LLM分析错误
            logger.info("🤖 正在分析错误...")
            llm_response = self.analyze_error_with_llm(test_result)
            
            logger.info(f"📊 错误分析: {llm_response.analysis}")
            logger.info(f"🔧 建议修复命令: {llm_response.fix_commands}")
            logger.info(f"📝 详细说明: {llm_response.explanation}")
            logger.info(f"🎯 置信度: {llm_response.confidence:.2f}")
            
            if not llm_response.fix_commands:
                logger.error("❌ 无法获取有效的修复建议")
                break
            
            # 执行修复命令
            logger.info("🔧 开始执行修复命令...")
            fix_results = self.execute_fix_commands(llm_response.fix_commands, working_dir)
            
            # 记录修复尝试
            attempt = FixAttempt(
                attempt_number=self.attempt_count,
                error_analysis=llm_response.analysis,
                fix_commands=llm_response.fix_commands,
                execution_results=fix_results,
                success=False,
                timestamp=time.time()
            )
            self.fix_history.append(attempt)
            
            # 检查修复命令是否成功
            successful_fixes = sum(1 for r in fix_results if r.success)
            logger.info(f"📈 修复命令成功率: {successful_fixes}/{len(fix_results)}")
            
            # 等待一段时间再重试
            logger.info("⏳ 等待系统稳定...")
            time.sleep(3)
        
        logger.error(f"💥 经过 {self.max_attempts} 次尝试后仍无法修复环境")
        self.save_fix_history()
        return False
    
    def save_fix_history(self):
        """保存修复历史"""
        history_file = "fix_history.json"
        
        history_data = []
        for attempt in self.fix_history:
            history_data.append({
                "attempt_number": attempt.attempt_number,
                "error_analysis": attempt.error_analysis,
                "fix_commands": attempt.fix_commands,
                "success": attempt.success,
                "timestamp": attempt.timestamp,
                "execution_results": [
                    {
                        "command": r.command,
                        "exit_code": r.exit_code,
                        "success": r.success,
                        "stderr": (r.stderr or "")[:200]  # 限制错误信息长度，处理None值
                    }
                    for r in attempt.execution_results
                ]
            })
        
        with open(history_file, 'w', encoding='utf-8') as f:
            json.dump(history_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📁 修复历史已保存到: {history_file}")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python3 enhanced_env_manager.py <测试命令> [工作目录] [--no-llm]")
        print("示例: python3 enhanced_env_manager.py 'python3 hello.py' /home/<USER>")
        print("选项: --no-llm  禁用LLM分析，仅使用本地规则")
        sys.exit(1)
    
    test_command = sys.argv[1]
    working_dir = sys.argv[2] if len(sys.argv) > 2 and not sys.argv[2].startswith('--') else "."
    use_llm = '--no-llm' not in sys.argv
    
    logger.info(f"🎯 测试命令: {test_command}")
    logger.info(f"📁 工作目录: {working_dir}")
    logger.info(f"🤖 LLM分析: {'启用' if use_llm else '禁用'}")
    
    manager = EnhancedEnvironmentManager(use_llm=use_llm)
    
    # 开始自动修复
    success = manager.auto_fix_environment(test_command, working_dir)
    
    if success:
        print("\\n🎉 环境配置成功完成！")
        print("✅ 系统已准备就绪，可以正常运行项目")
        sys.exit(0)
    else:
        print("\\n💥 环境配置失败")
        print("📋 请查看 fix_history.json 了解详细的修复尝试记录")
        print("🔧 建议手动检查系统配置或联系技术支持")
        sys.exit(1)

if __name__ == "__main__":
    main()
