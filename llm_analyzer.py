#!/usr/bin/env python3
"""
大语言模型错误分析器
使用LLM分析错误并生成修复方案
"""

import json
import requests
import os
from typing import Dict, List, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class LLMResponse:
    """LLM响应数据类"""
    analysis: str
    fix_commands: List[str]
    explanation: str
    confidence: float

class LLMAnalyzer:
    """大语言模型分析器"""
    
    def __init__(self, api_key: Optional[str] = None, model: str = "gpt-3.5-turbo"):
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        self.model = model
        self.base_url = "https://api.openai.com/v1/chat/completions"
        
    def analyze_error_with_llm(self, error_info: Dict) -> LLMResponse:
        """使用LLM分析错误"""
        
        prompt = f"""
你是一个专业的系统管理员和开发环境专家。请分析以下错误信息并提供修复方案：

错误信息：
- 命令: {error_info.get('command', 'N/A')}
- 退出码: {error_info.get('exit_code', 'N/A')}
- 标准输出: {error_info.get('stdout', 'N/A')}
- 错误输出: {error_info.get('stderr', 'N/A')}
- 工作目录: {error_info.get('working_dir', 'N/A')}

请提供：
1. 错误原因分析
2. 具体的修复命令（按优先级排序）
3. 详细解释
4. 置信度评分（0-1）

请以JSON格式回复：
{{
    "analysis": "错误原因分析",
    "fix_commands": ["命令1", "命令2", "命令3"],
    "explanation": "详细解释和注意事项",
    "confidence": 0.95
}}
"""

        if not self.api_key:
            # 如果没有API密钥，使用本地规则分析
            return self._fallback_analysis(error_info)
        
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.model,
                "messages": [
                    {"role": "system", "content": "你是一个专业的系统管理员和开发环境专家。"},
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.3,
                "max_tokens": 1000
            }
            
            response = requests.post(self.base_url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            content = result["choices"][0]["message"]["content"]
            
            # 尝试解析JSON响应
            try:
                llm_data = json.loads(content)
                return LLMResponse(
                    analysis=llm_data.get("analysis", ""),
                    fix_commands=llm_data.get("fix_commands", []),
                    explanation=llm_data.get("explanation", ""),
                    confidence=llm_data.get("confidence", 0.5)
                )
            except json.JSONDecodeError:
                # 如果不是JSON格式，尝试解析文本
                return self._parse_text_response(content)
                
        except Exception as e:
            logger.error(f"LLM分析失败: {e}")
            return self._fallback_analysis(error_info)
    
    def _fallback_analysis(self, error_info: Dict) -> LLMResponse:
        """备用分析方法（本地规则）"""
        stderr = error_info.get('stderr', '').lower()
        command = error_info.get('command', '')
        
        # Python相关错误
        if 'python3: not found' in stderr or 'python: not found' in stderr:
            return LLMResponse(
                analysis="Python未安装或不在系统PATH中",
                fix_commands=[
                    "sudo apt-get update && sudo apt-get install python3 python3-pip",
                    "brew install python3",
                    "winget install Python.Python.3"
                ],
                explanation="需要安装Python3。根据操作系统选择合适的安装命令。",
                confidence=0.9
            )
        
        # Node.js相关错误
        elif 'node: not found' in stderr or 'npm: not found' in stderr:
            return LLMResponse(
                analysis="Node.js或npm未安装",
                fix_commands=[
                    "curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash - && sudo apt-get install -y nodejs",
                    "brew install node",
                    "winget install OpenJS.NodeJS"
                ],
                explanation="需要安装Node.js和npm。建议安装LTS版本。",
                confidence=0.9
            )
        
        # 权限错误
        elif 'permission denied' in stderr:
            return LLMResponse(
                analysis="权限不足",
                fix_commands=[
                    f"chmod +x {command.split()[-1] if command else 'script'}",
                    f"sudo {command}",
                    "sudo chown $USER:$USER ."
                ],
                explanation="文件没有执行权限或需要管理员权限。",
                confidence=0.8
            )
        
        # 模块未找到
        elif 'modulenotfounderror' in stderr or 'no module named' in stderr:
            module_name = self._extract_module_name(stderr)
            return LLMResponse(
                analysis=f"Python模块 '{module_name}' 未安装",
                fix_commands=[
                    f"pip3 install {module_name}",
                    f"python3 -m pip install {module_name}",
                    f"sudo apt-get install python3-{module_name}"
                ],
                explanation=f"需要安装Python模块 {module_name}。",
                confidence=0.85
            )
        
        # 默认分析
        else:
            return LLMResponse(
                analysis="未知错误，需要进一步分析",
                fix_commands=[
                    "sudo apt-get update",
                    "which python3 || sudo apt-get install python3",
                    "python3 --version"
                ],
                explanation="建议检查基础环境配置。",
                confidence=0.3
            )
    
    def _extract_module_name(self, stderr: str) -> str:
        """从错误信息中提取模块名"""
        import re
        
        # 匹配 "No module named 'xxx'" 或 "ModuleNotFoundError: No module named 'xxx'"
        patterns = [
            r"no module named ['\"]([^'\"]+)['\"]",
            r"modulenotfounderror.*['\"]([^'\"]+)['\"]"
        ]
        
        for pattern in patterns:
            match = re.search(pattern, stderr.lower())
            if match:
                return match.group(1)
        
        return "unknown"
    
    def _parse_text_response(self, content: str) -> LLMResponse:
        """解析文本格式的LLM响应"""
        lines = content.strip().split('\n')
        
        analysis = ""
        fix_commands = []
        explanation = ""
        confidence = 0.5
        
        current_section = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            if '分析' in line or 'analysis' in line.lower():
                current_section = 'analysis'
            elif '命令' in line or 'command' in line.lower() or 'fix' in line.lower():
                current_section = 'commands'
            elif '解释' in line or 'explanation' in line.lower():
                current_section = 'explanation'
            elif current_section == 'analysis':
                analysis += line + " "
            elif current_section == 'commands':
                if line.startswith(('-', '*', '1.', '2.', '3.')):
                    # 提取命令
                    cmd = line.split(':', 1)[-1].strip()
                    if cmd and not cmd.startswith(('sudo', 'apt', 'brew', 'pip', 'npm')):
                        cmd = cmd.strip('`"\'')
                    if cmd:
                        fix_commands.append(cmd)
            elif current_section == 'explanation':
                explanation += line + " "
        
        return LLMResponse(
            analysis=analysis.strip(),
            fix_commands=fix_commands,
            explanation=explanation.strip(),
            confidence=confidence
        )

# 测试函数
def test_llm_analyzer():
    """测试LLM分析器"""
    analyzer = LLMAnalyzer()
    
    test_error = {
        'command': 'python3 hello.py',
        'exit_code': 127,
        'stdout': '',
        'stderr': 'sh: 1: python3: not found',
        'working_dir': '/home/<USER>'
    }
    
    result = analyzer.analyze_error_with_llm(test_error)
    
    print("=== LLM分析结果 ===")
    print(f"分析: {result.analysis}")
    print(f"修复命令: {result.fix_commands}")
    print(f"解释: {result.explanation}")
    print(f"置信度: {result.confidence}")

if __name__ == "__main__":
    test_llm_analyzer()
