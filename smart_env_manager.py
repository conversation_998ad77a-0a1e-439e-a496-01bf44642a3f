#!/usr/bin/env python3
"""
智能环境配置管理器
自动检测、分析和修复项目环境问题
"""

import os
import sys
import json
import subprocess
import time
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('env_manager.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ExecutionResult:
    """执行结果数据类"""
    command: str
    exit_code: int
    stdout: str
    stderr: str
    success: bool
    working_dir: str

@dataclass
class FixSuggestion:
    """修复建议数据类"""
    description: str
    script_content: str
    priority: int
    estimated_time: str

class ErrorAnalyzer:
    """错误分析器"""
    
    def __init__(self):
        self.common_errors = {
            "python3: not found": {
                "description": "Python3 未安装或不在PATH中",
                "solutions": [
                    "sudo apt-get update && sudo apt-get install python3",
                    "brew install python3",
                    "winget install Python.Python.3"
                ]
            },
            "pip: not found": {
                "description": "pip 未安装",
                "solutions": [
                    "sudo apt-get install python3-pip",
                    "python3 -m ensurepip --upgrade",
                    "curl https://bootstrap.pypa.io/get-pip.py | python3"
                ]
            },
            "node: not found": {
                "description": "Node.js 未安装",
                "solutions": [
                    "curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash - && sudo apt-get install -y nodejs",
                    "brew install node",
                    "winget install OpenJS.NodeJS"
                ]
            },
            "npm: not found": {
                "description": "npm 未安装",
                "solutions": [
                    "sudo apt-get install npm",
                    "brew install npm"
                ]
            }
        }
    
    def analyze_error(self, result: ExecutionResult) -> List[FixSuggestion]:
        """分析错误并返回修复建议"""
        suggestions = []
        
        error_text = result.stderr.lower()
        
        for error_pattern, error_info in self.common_errors.items():
            if error_pattern in error_text:
                for i, solution in enumerate(error_info["solutions"]):
                    suggestion = FixSuggestion(
                        description=f"{error_info['description']} - 解决方案 {i+1}",
                        script_content=solution,
                        priority=i + 1,
                        estimated_time="1-2分钟"
                    )
                    suggestions.append(suggestion)
        
        return suggestions

class ScriptGenerator:
    """脚本生成器"""
    
    def __init__(self):
        self.script_templates = {
            "linux": {
                "header": "#!/bin/bash\nset -e\necho '开始环境修复...'\n",
                "footer": "\necho '环境修复完成！'"
            },
            "windows": {
                "header": "@echo off\necho 开始环境修复...\n",
                "footer": "\necho 环境修复完成！"
            }
        }
    
    def generate_fix_script(self, suggestions: List[FixSuggestion], platform: str = "linux") -> str:
        """生成修复脚本"""
        if not suggestions:
            return ""
        
        template = self.script_templates.get(platform, self.script_templates["linux"])
        script_content = template["header"]
        
        for suggestion in sorted(suggestions, key=lambda x: x.priority):
            script_content += f"\n# {suggestion.description}\n"
            script_content += f"echo '执行: {suggestion.description}'\n"
            script_content += f"{suggestion.script_content}\n"
            script_content += "sleep 1\n"
        
        script_content += template["footer"]
        return script_content

class EnvironmentManager:
    """环境管理器主类"""
    
    def __init__(self, max_attempts: int = 5):
        self.max_attempts = max_attempts
        self.analyzer = ErrorAnalyzer()
        self.script_generator = ScriptGenerator()
        self.attempt_count = 0
        
    def execute_command(self, command: str, working_dir: str = ".") -> ExecutionResult:
        """执行命令并返回结果"""
        logger.info(f"执行命令: {command} (工作目录: {working_dir})")
        
        try:
            process = subprocess.run(
                command,
                shell=True,
                cwd=working_dir,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            result = ExecutionResult(
                command=command,
                exit_code=process.returncode,
                stdout=process.stdout,
                stderr=process.stderr,
                success=process.returncode == 0,
                working_dir=working_dir
            )
            
            logger.info(f"命令执行完成，退出码: {result.exit_code}")
            return result
            
        except subprocess.TimeoutExpired:
            logger.error(f"命令执行超时: {command}")
            return ExecutionResult(
                command=command,
                exit_code=-1,
                stdout="",
                stderr="命令执行超时",
                success=False,
                working_dir=working_dir
            )
        except Exception as e:
            logger.error(f"命令执行异常: {e}")
            return ExecutionResult(
                command=command,
                exit_code=-1,
                stdout="",
                stderr=str(e),
                success=False,
                working_dir=working_dir
            )
    
    def create_test_file(self, filename: str = "test_env.py") -> str:
        """创建测试文件"""
        test_content = '''#!/usr/bin/env python3
"""
环境测试文件
"""

import sys
import os

def test_python():
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    return True

def test_imports():
    try:
        import json
        import subprocess
        import pathlib
        print("基础模块导入成功")
        return True
    except ImportError as e:
        print(f"模块导入失败: {e}")
        return False

def main():
    print("=== 环境测试开始 ===")
    
    success = True
    success &= test_python()
    success &= test_imports()
    
    if success:
        print("✅ 环境测试通过！")
        return 0
    else:
        print("❌ 环境测试失败！")
        return 1

if __name__ == "__main__":
    sys.exit(main())
'''
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        # 设置执行权限
        os.chmod(filename, 0o755)
        logger.info(f"创建测试文件: {filename}")
        return filename
    
    def auto_fix_environment(self, test_command: str, working_dir: str = ".") -> bool:
        """自动修复环境"""
        logger.info("开始自动环境修复流程")
        
        while self.attempt_count < self.max_attempts:
            self.attempt_count += 1
            logger.info(f"第 {self.attempt_count} 次尝试修复")
            
            # 执行测试命令
            result = self.execute_command(test_command, working_dir)
            
            if result.success:
                logger.info("✅ 环境测试通过！修复成功")
                return True
            
            logger.warning(f"❌ 环境测试失败，退出码: {result.exit_code}")
            logger.warning(f"错误输出: {result.stderr}")
            
            # 分析错误
            suggestions = self.analyzer.analyze_error(result)
            
            if not suggestions:
                logger.error("无法找到合适的修复建议")
                break
            
            # 生成修复脚本
            platform = "windows" if os.name == 'nt' else "linux"
            script_content = self.script_generator.generate_fix_script(suggestions, platform)
            
            if not script_content:
                logger.error("无法生成修复脚本")
                break
            
            # 保存并执行修复脚本
            script_filename = f"fix_script_{self.attempt_count}.{'bat' if platform == 'windows' else 'sh'}"
            
            with open(script_filename, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            if platform != "windows":
                os.chmod(script_filename, 0o755)
            
            logger.info(f"执行修复脚本: {script_filename}")
            
            # 执行修复脚本
            fix_command = script_filename if platform == "windows" else f"./{script_filename}"
            fix_result = self.execute_command(fix_command, working_dir)
            
            if not fix_result.success:
                logger.warning(f"修复脚本执行失败: {fix_result.stderr}")
            
            # 等待一段时间再重试
            time.sleep(2)
        
        logger.error(f"经过 {self.max_attempts} 次尝试后仍无法修复环境")
        return False

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python3 smart_env_manager.py <测试命令> [工作目录]")
        print("示例: python3 smart_env_manager.py 'python3 hello.py' /home/<USER>")
        sys.exit(1)
    
    test_command = sys.argv[1]
    working_dir = sys.argv[2] if len(sys.argv) > 2 else "."
    
    manager = EnvironmentManager()
    
    # 创建测试文件
    test_file = manager.create_test_file()
    
    # 开始自动修复
    success = manager.auto_fix_environment(test_command, working_dir)
    
    if success:
        print("🎉 环境配置成功完成！")
        sys.exit(0)
    else:
        print("💥 环境配置失败，请手动检查")
        sys.exit(1)

if __name__ == "__main__":
    main()
