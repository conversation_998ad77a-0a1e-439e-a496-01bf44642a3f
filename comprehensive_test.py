#!/usr/bin/env python3
"""
综合环境测试文件
测试Python环境、基础模块和系统工具
"""

import sys
import os
import subprocess
import platform

def test_python_environment():
    """测试Python环境"""
    print("=== Python环境测试 ===")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"平台信息: {platform.platform()}")
    return True

def test_basic_modules():
    """测试基础模块"""
    print("\n=== 基础模块测试 ===")
    modules = ['json', 'subprocess', 'pathlib', 'urllib', 'sqlite3']
    
    for module in modules:
        try:
            __import__(module)
            print(f"✅ {module}: 可用")
        except ImportError:
            print(f"❌ {module}: 不可用")
            return False
    
    return True

def test_system_tools():
    """测试系统工具"""
    print("\n=== 系统工具测试 ===")
    tools = ['git', 'curl', 'wget']
    
    for tool in tools:
        try:
            result = subprocess.run([tool, '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print(f"✅ {tool}: 可用")
            else:
                print(f"⚠️ {tool}: 可能不可用")
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print(f"❌ {tool}: 不可用")

def test_network_connectivity():
    """测试网络连接"""
    print("\n=== 网络连接测试 ===")
    try:
        import urllib.request
        urllib.request.urlopen('https://www.google.com', timeout=5)
        print("✅ 网络连接: 正常")
        return True
    except:
        print("❌ 网络连接: 异常")
        return False

def main():
    """主测试函数"""
    print("🚀 开始综合环境测试...")
    
    success = True
    success &= test_python_environment()
    success &= test_basic_modules()
    test_system_tools()  # 系统工具不影响主要测试结果
    test_network_connectivity()  # 网络连接不影响主要测试结果
    
    print("\n" + "="*50)
    if success:
        print("🎉 环境测试通过！系统已准备就绪")
        return 0
    else:
        print("💥 环境测试失败！需要修复环境")
        return 1

if __name__ == "__main__":
    sys.exit(main())
