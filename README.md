# 智能环境配置管理器

一个基于大语言模型的智能项目环境自动配置系统，能够自动检测、分析和修复项目运行环境问题。

## 🌟 功能特性

- **🤖 智能错误分析**: 集成大语言模型，深度分析环境错误
- **🔧 自动修复**: 根据分析结果自动生成并执行修复脚本
- **🔄 循环修复**: 持续尝试直到环境配置成功
- **📊 详细记录**: 完整的修复历史和执行日志
- **🛡️ 备用方案**: 本地规则分析，无需依赖外部API
- **🎯 多平台支持**: 支持Linux、macOS和Windows

## 📦 文件结构

```
├── enhanced_env_manager.py    # 主程序 - 增强版环境管理器
├── llm_analyzer.py           # LLM错误分析器
├── smart_env_manager.py      # 基础版环境管理器
├── hello.py                  # 测试文件
├── demo_usage.py            # 使用演示
└── README.md                # 说明文档
```

## 🚀 快速开始

### 基本用法

```bash
# 测试Python环境
python3 enhanced_env_manager.py "python3 hello.py"

# 测试Node.js项目
python3 enhanced_env_manager.py "node app.js" /path/to/project

# 禁用LLM分析（仅使用本地规则）
python3 enhanced_env_manager.py "python3 hello.py" --no-llm
```

### 环境变量配置（可选）

如果要使用LLM分析功能，可以设置OpenAI API密钥：

```bash
export OPENAI_API_KEY="your-api-key-here"
```

## 📋 使用场景

### 场景1：Python环境问题

**问题**: `python3: not found`

**自动修复**:
```bash
sudo apt-get update
sudo apt-get install python3 python3-pip
python3 --version
```

### 场景2：Node.js环境问题

**问题**: `node: not found`

**自动修复**:
```bash
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt-get install -y nodejs
node --version
```

### 场景3：权限问题

**问题**: `permission denied`

**自动修复**:
```bash
chmod +x script_name
# 或
sudo chown $USER:$USER .
```

## 🔧 工作流程

1. **执行测试命令** - 运行用户指定的测试命令
2. **错误检测** - 捕获执行失败的详细信息
3. **智能分析** - 使用LLM或本地规则分析错误原因
4. **生成修复方案** - 自动生成针对性的修复命令
5. **执行修复** - 自动运行修复脚本
6. **验证结果** - 重新测试，确认问题是否解决
7. **循环重试** - 如果仍有问题，重复上述流程

## 📊 输出示例

```
🚀 开始智能环境修复流程

📋 第 1/5 次修复尝试
执行命令: python3 hello.py (工作目录: .)
❌ 命令执行失败: python3 hello.py (退出码: 127)

🤖 正在分析错误...
📊 错误分析: Python3未安装或不在PATH中
🔧 建议修复命令: ['sudo apt-get update', 'sudo apt-get install python3 python3-pip', 'python3 --version']
📝 详细说明: 需要安装Python3运行环境
🎯 置信度: 0.90

🔧 开始执行修复命令...
执行修复命令 1/3: sudo apt-get update
✅ 修复命令成功: sudo apt-get update
...

🎉 环境测试通过！修复成功完成
✅ 系统已准备就绪，可以正常运行项目
```

## 📁 生成的文件

- `comprehensive_test.py` - 综合环境测试文件
- `fix_history.json` - 详细的修复历史记录
- `enhanced_env_manager.log` - 完整的执行日志
- `fix_script_*.sh/.bat` - 生成的修复脚本

## 🛠️ 高级配置

### 自定义最大重试次数

```python
manager = EnhancedEnvironmentManager(max_attempts=10)
```

### 自定义LLM模型

```python
llm_analyzer = LLMAnalyzer(model="gpt-4")
```

### 添加自定义错误规则

在 `llm_analyzer.py` 中的 `_fallback_analysis` 方法中添加新的错误模式。

## 🔍 故障排除

### 常见问题

1. **LLM分析失败**: 检查网络连接和API密钥
2. **权限不足**: 使用 `sudo` 运行或调整文件权限
3. **网络问题**: 检查防火墙和代理设置

### 调试模式

查看详细日志：
```bash
tail -f enhanced_env_manager.log
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License

## 🙏 致谢

感谢所有为开源社区做出贡献的开发者们！
