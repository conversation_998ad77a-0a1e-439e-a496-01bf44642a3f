#!/usr/bin/env python3
"""
简化版智能环境配置管理器
避免编码问题的版本
"""

import os
import sys
import json
import subprocess
import time
import logging
from typing import Dict, List, Optional
from dataclasses import dataclass

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('simple_env_manager.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ExecutionResult:
    """执行结果数据类"""
    command: str
    exit_code: int
    stdout: str
    stderr: str
    success: bool
    working_dir: str

@dataclass
class FixSuggestion:
    """修复建议数据类"""
    description: str
    commands: List[str]
    confidence: float

class SimpleEnvironmentManager:
    """简化版环境管理器"""
    
    def __init__(self, max_attempts: int = 5):
        self.max_attempts = max_attempts
        self.attempt_count = 0
        
    def execute_command(self, command: str, working_dir: str = ".") -> ExecutionResult:
        """执行命令并返回结果"""
        logger.info(f"执行命令: {command}")
        
        try:
            process = subprocess.run(
                command,
                shell=True,
                cwd=working_dir,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            result = ExecutionResult(
                command=command,
                exit_code=process.returncode,
                stdout=process.stdout or "",
                stderr=process.stderr or "",
                success=process.returncode == 0,
                working_dir=working_dir
            )
            
            if result.success:
                logger.info(f"命令执行成功: {command}")
            else:
                logger.warning(f"命令执行失败: {command} (退出码: {result.exit_code})")
            
            return result
            
        except Exception as e:
            logger.error(f"命令执行异常: {e}")
            return ExecutionResult(
                command=command,
                exit_code=-1,
                stdout="",
                stderr=str(e),
                success=False,
                working_dir=working_dir
            )
    
    def analyze_error(self, result: ExecutionResult) -> FixSuggestion:
        """分析错误并返回修复建议"""
        stderr = (result.stderr or "").lower()
        is_windows = os.name == 'nt'
        
        # Python相关错误
        if any(x in stderr for x in ['python3: not found', 'python: not found', "'python3' is not recognized", "'python' is not recognized"]):
            if is_windows:
                return FixSuggestion(
                    description="Python未安装或不在PATH中",
                    commands=["python --version", "py --version"],
                    confidence=0.8
                )
            else:
                return FixSuggestion(
                    description="Python3未安装",
                    commands=[
                        "sudo apt-get update",
                        "sudo apt-get install python3 python3-pip",
                        "python3 --version"
                    ],
                    confidence=0.9
                )
        
        # Node.js相关错误
        elif any(x in stderr for x in ['node: not found', "'node' is not recognized"]):
            if is_windows:
                return FixSuggestion(
                    description="Node.js未安装",
                    commands=["winget install OpenJS.NodeJS"],
                    confidence=0.9
                )
            else:
                return FixSuggestion(
                    description="Node.js未安装",
                    commands=[
                        "curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -",
                        "sudo apt-get install -y nodejs"
                    ],
                    confidence=0.9
                )
        
        # 权限错误
        elif 'permission denied' in stderr:
            return FixSuggestion(
                description="权限不足",
                commands=[f"chmod +x {result.command.split()[-1]}"],
                confidence=0.8
            )
        
        # 默认建议
        else:
            return FixSuggestion(
                description="未知错误",
                commands=["echo 检查系统环境"],
                confidence=0.3
            )
    
    def create_test_file(self, filename: str = "test_simple.py") -> str:
        """创建简单测试文件"""
        test_content = '''#!/usr/bin/env python3
import sys
import os
from datetime import datetime

def main():
    print("Hello World!")
    print(f"Python版本: {sys.version}")
    print(f"当前目录: {os.getcwd()}")
    print("环境测试通过!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
'''
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        try:
            os.chmod(filename, 0o755)
        except:
            pass  # Windows可能不支持chmod
        
        logger.info(f"创建测试文件: {filename}")
        return filename
    
    def auto_fix_environment(self, test_command: str, working_dir: str = ".") -> bool:
        """自动修复环境"""
        logger.info("开始自动环境修复")
        
        # 创建测试文件
        test_file = self.create_test_file()
        
        while self.attempt_count < self.max_attempts:
            self.attempt_count += 1
            logger.info(f"第 {self.attempt_count} 次尝试修复")
            
            # 执行测试命令
            result = self.execute_command(test_command, working_dir)
            
            if result.success:
                logger.info("环境测试通过！修复成功")
                return True
            
            logger.warning(f"环境测试失败，退出码: {result.exit_code}")
            logger.warning(f"错误输出: {result.stderr}")
            
            # 分析错误
            suggestion = self.analyze_error(result)
            logger.info(f"错误分析: {suggestion.description}")
            logger.info(f"修复命令: {suggestion.commands}")
            logger.info(f"置信度: {suggestion.confidence}")
            
            if not suggestion.commands:
                logger.error("无法找到修复建议")
                break
            
            # 执行修复命令
            for i, cmd in enumerate(suggestion.commands, 1):
                logger.info(f"执行修复命令 {i}/{len(suggestion.commands)}: {cmd}")
                fix_result = self.execute_command(cmd, working_dir)
                
                if not fix_result.success:
                    logger.warning(f"修复命令失败: {cmd}")
                    logger.warning(f"错误: {fix_result.stderr}")
                else:
                    logger.info(f"修复命令成功: {cmd}")
                
                time.sleep(1)
            
            # 等待一段时间再重试
            time.sleep(2)
        
        logger.error(f"经过 {self.max_attempts} 次尝试后仍无法修复环境")
        return False

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python simple_env_manager.py <测试命令> [工作目录]")
        print("示例: python simple_env_manager.py 'python hello.py' .")
        sys.exit(1)
    
    test_command = sys.argv[1]
    working_dir = sys.argv[2] if len(sys.argv) > 2 else "."
    
    print(f"测试命令: {test_command}")
    print(f"工作目录: {working_dir}")
    
    manager = SimpleEnvironmentManager()
    
    # 开始自动修复
    success = manager.auto_fix_environment(test_command, working_dir)
    
    if success:
        print("\n环境配置成功完成！")
        print("系统已准备就绪，可以正常运行项目")
        sys.exit(0)
    else:
        print("\n环境配置失败")
        print("建议手动检查系统配置")
        sys.exit(1)

if __name__ == "__main__":
    main()
